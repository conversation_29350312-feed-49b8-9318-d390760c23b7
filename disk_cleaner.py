# disk_cleaner.py
# A simple cross-platform GUI tool to clean and (optionally) format connected disks.

import os
import re
import subprocess
import platform
import tkinter as tk
from tkinter import messagebox


def list_disks():
    """
    Return a list of connected disks with device paths and descriptions.
    On Windows, uses WMIC to list physical drives.
    On Linux/macOS, uses lsblk to list block devices.
    """
    disks = []
    system = platform.system()
    if system == 'Windows':
        # List physical drives
        result = subprocess.run(['wmic', 'diskdrive', 'get', 'DeviceID,Model,Size'],
                                capture_output=True, text=True)
        lines = result.stdout.strip().splitlines()
        for line in lines[1:]:
            if line.strip():
                parts = line.split()
                device = parts[0]
                size = parts[-1]
                model = ' '.join(parts[1:-1])
                m = re.search(r'PHYSICALDRIVE(\d+)', device, re.IGNORECASE)
                index = m.group(1) if m else None
                desc = f"{model} ({int(size)/1e9:.2f} GB)"
                disks.append({'device': device, 'index': index, 'description': desc})
    else:
        # Linux/macOS
        result = subprocess.run(['lsblk', '-d', '-o', 'NAME,SIZE,MODEL'],
                                capture_output=True, text=True)
        lines = result.stdout.strip().splitlines()
        for line in lines[1:]:
            parts = line.split()
            if len(parts) >= 2:
                name = parts[0]
                size = parts[1]
                model = ' '.join(parts[2:]) if len(parts) > 2 else ''
                device = f"/dev/{name}"
                desc = f"{model} ({size})"
                disks.append({'device': device, 'description': desc})
    return disks


def clean_disk(disk):
    """
    Clean (wipe partition table) on the selected disk.
    Requires admin privileges (run as Administrator or with sudo).
    """
    system = platform.system()
    if system == 'Windows':
        # Use diskpart to clean the disk by index
        script = f"select disk {disk['index']}\nclean\n"
        p = subprocess.run(['diskpart'], input=script,
                           capture_output=True, text=True)
        return p.stdout, p.stderr
    else:
        # Zero out the first sector
        p = subprocess.run(['sudo', 'dd', 'if=/dev/zero', f"of={disk['device']}",
                            'bs=512', 'count=1'], capture_output=True, text=True)
        return p.stdout, p.stderr


def format_disk(disk, fs):
    """
    Format the cleaned disk with the selected file system.
    """
    system = platform.system()
    if system == 'Windows':
        # Format volume (e.g., D:) with the chosen FS
        # Assuming disk['device'] corresponds to a drive letter
        drive = disk['device'].rstrip('\\')
        cmd = ['format', drive, f'/FS:{fs}', '/Q', '/Y']
        p = subprocess.run(cmd, input='\n', capture_output=True, text=True)
        return p.stdout, p.stderr
    else:
        if fs == 'FAT32':
            mkcmd = ['sudo', 'mkfs.vfat', disk['device']]
        elif fs == 'NTFS':
            mkcmd = ['sudo', 'mkfs.ntfs', disk['device']]
        elif fs == 'exFAT':
            mkcmd = ['sudo', 'mkfs.exfat', disk['device']]
        else:
            return '', f"Unsupported file system: {fs}"
        p = subprocess.run(mkcmd, capture_output=True, text=True)
        return p.stdout, p.stderr


def on_clean():
    sel = listbox.curselection()
    if not sel:
        messagebox.showwarning("No Selection", "Please select a disk to clean.")
        return
    disk = disks[sel[0]]
    if not messagebox.askyesno("Confirm Clean",
                               f"Are you sure you want to clean disk {disk['device']}? This will erase all data."):
        return
    out, err = clean_disk(disk)
    if err:
        messagebox.showerror("Clean Failed", err)
        return
    messagebox.showinfo("Clean Complete", f"Disk {disk['device']} cleaned successfully.")
    if messagebox.askyesno("Format Disk", "Do you want to format the drive now?"):
        show_format_window(disk)


def show_format_window(disk):
    fmt_win = tk.Toplevel(root)
    fmt_win.title("Format Disk")
    tk.Label(fmt_win, text="Select file system:").pack(padx=10, pady=5)
    fs_var = tk.StringVar(fmt_win)
    fs_var.set("FAT32")
    tk.OptionMenu(fmt_win, fs_var, "FAT32", "NTFS", "exFAT").pack(padx=10, pady=5)

    def do_format():
        fs = fs_var.get()
        out, err = format_disk(disk, fs)
        if err:
            messagebox.showerror("Format Failed", err)
        else:
            messagebox.showinfo("Format Complete", f"Drive formatted as {fs} successfully.\n{out}")
        fmt_win.destroy()

    tk.Button(fmt_win, text="Format", command=do_format).pack(pady=10)


if __name__ == '__main__':
    disks = list_disks()
    root = tk.Tk()
    root.title("Disk Cleaner")
    listbox = tk.Listbox(root, width=60)
    for d in disks:
        desc = d.get('description', '')
        listbox.insert(tk.END, f"{d['device']} — {desc}")
    listbox.pack(padx=10, pady=10)
    tk.Button(root, text="Clean Selected Disk", command=on_clean).pack(pady=5)
    root.mainloop()
