using System.Diagnostics;
using System.Text.RegularExpressions;

namespace DiskCleaner
{
    /// <summary>
    /// Service class for disk operations
    /// </summary>
    public static class DiskService
    {
        /// <summary>
        /// Lists all connected disks with device paths and descriptions
        /// </summary>
        /// <returns>List of disk information</returns>
        public static async Task<List<DiskInfo>> ListDisksAsync()
        {
            var disks = new List<DiskInfo>();

            if (OperatingSystem.IsWindows())
            {
                await ListWindowsDisksAsync(disks);
            }
            else if (OperatingSystem.IsLinux() || OperatingSystem.IsMacOS())
            {
                await ListUnixDisksAsync(disks);
            }

            return disks;
        }

        private static async Task ListWindowsDisksAsync(List<DiskInfo> disks)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "wmic",
                    Arguments = "diskdrive where \"Index!=0 and Index!=1\" get Index, Model, Size", //diskdrive get DeviceID,Model,Size - < original command
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };
                
                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    var output = await process.StandardOutput.ReadToEndAsync();
                    await process.WaitForExitAsync();

                    var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                    for (int i = 1; i < lines.Length; i++)
                    {
                        var line = lines[i].Trim();
                        if (string.IsNullOrEmpty(line)) continue;

                        var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 3)
                        {
                            // With the new command format: Index, Model, Size
                            var index = parts[0];
                            var size = parts[^1];
                            var model = string.Join(" ", parts[1..^1]);

                            // Create the device path from the index
                            var device = $"Disk {index}";

                            if (long.TryParse(size, out var sizeBytes))
                            {
                                var sizeGB = sizeBytes / 1e9;
                                var description = $"{model} ({sizeGB:F2} GB)";

                                disks.Add(new DiskInfo
                                {
                                    Device = device,
                                    Index = index,
                                    Description = description,
                                    Model = model,
                                    Size = sizeBytes
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to list Windows disks: {ex.Message}", ex);
            }
        }

        private static async Task ListUnixDisksAsync(List<DiskInfo> disks)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "lsblk",
                    Arguments = "-d -o NAME,SIZE,MODEL",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    var output = await process.StandardOutput.ReadToEndAsync();
                    await process.WaitForExitAsync();

                    var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                    for (int i = 1; i < lines.Length; i++)
                    {
                        var line = lines[i].Trim();
                        if (string.IsNullOrEmpty(line)) continue;

                        var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 2)
                        {
                            var name = parts[0];
                            var size = parts[1];
                            var model = parts.Length > 2 ? string.Join(" ", parts[2..]) : "";
                            var device = $"/dev/{name}";
                            var description = $"{model} ({size})";

                            disks.Add(new DiskInfo
                            {
                                Device = device,
                                Description = description,
                                Model = model,
                                Size = 0 // Size parsing for Unix would need additional logic
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to list Unix disks: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Cleans (wipes partition table) on the selected disk
        /// </summary>
        /// <param name="disk">The disk to clean</param>
        /// <returns>Tuple of stdout and stderr</returns>
        public static async Task<(string stdout, string stderr)> CleanDiskAsync(DiskInfo disk)
        {
            if (OperatingSystem.IsWindows())
            {
                return await CleanWindowsDiskAsync(disk);
            }
            else if (OperatingSystem.IsLinux() || OperatingSystem.IsMacOS())
            {
                return await CleanUnixDiskAsync(disk);
            }

            throw new PlatformNotSupportedException("Unsupported operating system");
        }

        private static async Task<(string stdout, string stderr)> CleanWindowsDiskAsync(DiskInfo disk)
        {
            if (string.IsNullOrEmpty(disk.Index))
            {
                return ("", "Invalid disk index");
            }

            var script = $"select disk {disk.Index}\nclean\n";

            var startInfo = new ProcessStartInfo
            {
                FileName = "diskpart",
                UseShellExecute = false,
                RedirectStandardInput = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using var process = Process.Start(startInfo);
            if (process != null)
            {
                await process.StandardInput.WriteAsync(script);
                process.StandardInput.Close();

                var stdout = await process.StandardOutput.ReadToEndAsync();
                var stderr = await process.StandardError.ReadToEndAsync();
                await process.WaitForExitAsync();

                return (stdout, stderr);
            }

            return ("", "Failed to start diskpart");
        }

        private static async Task<(string stdout, string stderr)> CleanUnixDiskAsync(DiskInfo disk)
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = "sudo",
                Arguments = $"dd if=/dev/zero of={disk.Device} bs=512 count=1",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using var process = Process.Start(startInfo);
            if (process != null)
            {
                var stdout = await process.StandardOutput.ReadToEndAsync();
                var stderr = await process.StandardError.ReadToEndAsync();
                await process.WaitForExitAsync();

                return (stdout, stderr);
            }

            return ("", "Failed to start dd command");
        }

        /// <summary>
        /// Formats the cleaned disk with the selected file system
        /// </summary>
        /// <param name="disk">The disk to format</param>
        /// <param name="fileSystem">The file system (FAT32, NTFS, exFAT)</param>
        /// <returns>Tuple of stdout and stderr</returns>
        public static async Task<(string stdout, string stderr)> FormatDiskAsync(DiskInfo disk, string fileSystem)
        {
            if (OperatingSystem.IsWindows())
            {
                return await FormatWindowsDiskAsync(disk, fileSystem);
            }
            else if (OperatingSystem.IsLinux() || OperatingSystem.IsMacOS())
            {
                return await FormatUnixDiskAsync(disk, fileSystem);
            }

            throw new PlatformNotSupportedException("Unsupported operating system");
        }

        private static async Task<(string stdout, string stderr)> FormatWindowsDiskAsync(DiskInfo disk, string fileSystem)
        {
            if (string.IsNullOrEmpty(disk.Index))
            {
                return ("", "Invalid disk index");
            }

            // Convert file system name for diskpart
            string fsType = fileSystem.ToUpper() switch
            {
                "FAT32" => "FAT32",
                "NTFS" => "NTFS",
                "EXFAT" => "EXFAT",
                _ => "NTFS" // Default to NTFS
            };

            // Create diskpart script to create partition and format
            var script = $@"select disk {disk.Index}
create partition primary
active
format fs={fsType} quick
assign
";

            var startInfo = new ProcessStartInfo
            {
                FileName = "diskpart",
                UseShellExecute = false,
                RedirectStandardInput = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using var process = Process.Start(startInfo);
            if (process != null)
            {
                await process.StandardInput.WriteAsync(script);
                process.StandardInput.Close();

                var stdout = await process.StandardOutput.ReadToEndAsync();
                var stderr = await process.StandardError.ReadToEndAsync();
                await process.WaitForExitAsync();

                return (stdout, stderr);
            }

            return ("", "Failed to start diskpart");
        }

        private static async Task<(string stdout, string stderr)> FormatUnixDiskAsync(DiskInfo disk, string fileSystem)
        {
            string command;
            switch (fileSystem.ToUpper())
            {
                case "FAT32":
                    command = $"sudo mkfs.vfat {disk.Device}";
                    break;
                case "NTFS":
                    command = $"sudo mkfs.ntfs {disk.Device}";
                    break;
                case "EXFAT":
                    command = $"sudo mkfs.exfat {disk.Device}";
                    break;
                default:
                    return ("", $"Unsupported file system: {fileSystem}");
            }

            var parts = command.Split(' ', 2);
            var startInfo = new ProcessStartInfo
            {
                FileName = parts[0],
                Arguments = parts.Length > 1 ? parts[1] : "",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using var process = Process.Start(startInfo);
            if (process != null)
            {
                var stdout = await process.StandardOutput.ReadToEndAsync();
                var stderr = await process.StandardError.ReadToEndAsync();
                await process.WaitForExitAsync();

                return (stdout, stderr);
            }

            return ("", $"Failed to start {parts[0]} command");
        }
    }
}
