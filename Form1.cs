namespace DiskCleaner
{
    public partial class Form1 : Form
    {
        private List<DiskInfo> _disks = new();

        public Form1()
        {
            InitializeComponent();
        }

        private async void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                await LoadDisksAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadDisksAsync()
        {
            try
            {
                listBoxDisks.Items.Clear();
                listBoxDisks.Items.Add("Loading disks...");
                listBoxDisks.Enabled = false;
                buttonClean.Enabled = false;

                _disks = await DiskService.ListDisksAsync();

                listBoxDisks.Items.Clear();
                foreach (var disk in _disks)
                {
                    listBoxDisks.Items.Add(disk.ToString());
                }

                listBoxDisks.Enabled = true;
                buttonClean.Enabled = true;

                if (_disks.Count == 0)
                {
                    listBoxDisks.Items.Add("No disks found");
                    buttonClean.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to load disks: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                listBoxDisks.Items.Clear();
                listBoxDisks.Items.Add("Failed to load disks");
                buttonClean.Enabled = false;
            }
        }

        private async void buttonClean_Click(object sender, EventArgs e)
        {
            if (listBoxDisks.SelectedIndex < 0 || listBoxDisks.SelectedIndex >= _disks.Count)
            {
                MessageBox.Show("Please select a disk to clean.", "No Selection",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedDisk = _disks[listBoxDisks.SelectedIndex];

            var result = MessageBox.Show(
                $"Are you sure you want to clean disk {selectedDisk.Device}? This will erase all data.",
                "Confirm Clean",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result != DialogResult.Yes)
                return;

            try
            {
                buttonClean.Enabled = false;
                listBoxDisks.Enabled = false;

                var (stdout, stderr) = await DiskService.CleanDiskAsync(selectedDisk);

                if (!string.IsNullOrEmpty(stderr))
                {
                    MessageBox.Show($"Clean Failed: {stderr}", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                MessageBox.Show($"Disk {selectedDisk.Device} cleaned successfully.", "Clean Complete",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                var formatResult = MessageBox.Show("Do you want to format the drive now?", "Format Disk",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (formatResult == DialogResult.Yes)
                {
                    ShowFormatDialog(selectedDisk);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Clean Failed: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                buttonClean.Enabled = true;
                listBoxDisks.Enabled = true;
            }
        }

        private void ShowFormatDialog(DiskInfo disk)
        {
            using var formatDialog = new FormatDialog(disk);
            formatDialog.ShowDialog(this);
        }
    }
}
