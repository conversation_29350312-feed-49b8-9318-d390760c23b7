namespace DiskCleaner
{
    /// <summary>
    /// Represents information about a disk drive
    /// </summary>
    public class DiskInfo
    {
        public string Device { get; set; } = string.Empty;
        public string? Index { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public long Size { get; set; }

        public override string ToString()
        {
            return $"{Device} — {Description}";
        }
    }
}
