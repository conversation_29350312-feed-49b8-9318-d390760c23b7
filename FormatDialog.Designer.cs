namespace DiskCleaner
{
    partial class FormatDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private Label labelInstruction;
        private ComboBox comboBoxFileSystem;
        private Button buttonFormat;
        private Button buttonCancel;
        private Label labelStatus;

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            labelInstruction = new Label();
            comboBoxFileSystem = new ComboBox();
            buttonFormat = new Button();
            buttonCancel = new Button();
            labelStatus = new Label();
            SuspendLayout();
            // 
            // labelInstruction
            // 
            labelInstruction.AutoSize = true;
            labelInstruction.Location = new Point(12, 15);
            labelInstruction.Name = "labelInstruction";
            labelInstruction.Size = new Size(108, 15);
            labelInstruction.TabIndex = 0;
            labelInstruction.Text = "Select file system:";
            // 
            // comboBoxFileSystem
            // 
            comboBoxFileSystem.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxFileSystem.FormattingEnabled = true;
            comboBoxFileSystem.Location = new Point(12, 40);
            comboBoxFileSystem.Name = "comboBoxFileSystem";
            comboBoxFileSystem.Size = new Size(200, 23);
            comboBoxFileSystem.TabIndex = 1;
            // 
            // buttonFormat
            // 
            buttonFormat.Location = new Point(12, 80);
            buttonFormat.Name = "buttonFormat";
            buttonFormat.Size = new Size(75, 30);
            buttonFormat.TabIndex = 2;
            buttonFormat.Text = "Format";
            buttonFormat.UseVisualStyleBackColor = true;
            buttonFormat.Click += buttonFormat_Click;
            // 
            // buttonCancel
            // 
            buttonCancel.Location = new Point(100, 80);
            buttonCancel.Name = "buttonCancel";
            buttonCancel.Size = new Size(75, 30);
            buttonCancel.TabIndex = 3;
            buttonCancel.Text = "Cancel";
            buttonCancel.UseVisualStyleBackColor = true;
            buttonCancel.Click += buttonCancel_Click;
            // 
            // labelStatus
            // 
            labelStatus.AutoSize = true;
            labelStatus.ForeColor = Color.Blue;
            labelStatus.Location = new Point(12, 120);
            labelStatus.Name = "labelStatus";
            labelStatus.Size = new Size(77, 15);
            labelStatus.TabIndex = 4;
            labelStatus.Text = "Formatting...";
            labelStatus.Visible = false;
            // 
            // FormatDialog
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(224, 151);
            Controls.Add(labelStatus);
            Controls.Add(buttonCancel);
            Controls.Add(buttonFormat);
            Controls.Add(comboBoxFileSystem);
            Controls.Add(labelInstruction);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "FormatDialog";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Format Disk";
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
    }
}
