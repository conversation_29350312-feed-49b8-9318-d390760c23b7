namespace DiskCleaner
{
    public partial class FormatDialog : Form
    {
        private readonly DiskInfo _disk;
        public string SelectedFileSystem { get; private set; } = "FAT32";

        public FormatDialog(DiskInfo disk)
        {
            _disk = disk;
            InitializeComponent();
            LoadFileSystemOptions();
        }

        private void LoadFileSystemOptions()
        {
            comboBoxFileSystem.Items.AddRange(new[] { "FAT32", "NTFS", "exFAT" });
            comboBoxFileSystem.SelectedIndex = 0; // Default to FAT32
        }

        private async void buttonFormat_Click(object sender, EventArgs e)
        {
            SelectedFileSystem = comboBoxFileSystem.SelectedItem?.ToString() ?? "FAT32";

            try
            {
                buttonFormat.Enabled = false;
                buttonCancel.Enabled = false;
                labelStatus.Text = $"Formatting as {SelectedFileSystem}...";
                labelStatus.Visible = true;

                var (stdout, stderr) = await DiskService.FormatDiskAsync(_disk, SelectedFileSystem);

                if (!string.IsNullOrEmpty(stderr))
                {
                    MessageBox.Show($"Format Failed: {stderr}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    MessageBox.Show($"Drive formatted as {SelectedFileSystem} successfully.\n{stdout}", 
                        "Format Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    DialogResult = DialogResult.OK;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Format Failed: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                buttonFormat.Enabled = true;
                buttonCancel.Enabled = true;
                labelStatus.Visible = false;
            }
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}
